"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/debug-products/page",{

/***/ "(app-pages-browser)/./src/lib/init-sample-products.ts":
/*!*****************************************!*\
  !*** ./src/lib/init-sample-products.ts ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cleanDuplicateProducts: () => (/* binding */ cleanDuplicateProducts),\n/* harmony export */   initSampleProducts: () => (/* binding */ initSampleProducts),\n/* harmony export */   resetSampleProducts: () => (/* binding */ resetSampleProducts)\n/* harmony export */ });\n/* harmony import */ var _products_store__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./products-store */ \"(app-pages-browser)/./src/lib/products-store.ts\");\n// إضافة منتجات تجريبية للاختبار\n\nconst initSampleProducts = ()=>{\n    // التحقق من وجود منتجات تجريبية محددة\n    const existingProducts = _products_store__WEBPACK_IMPORTED_MODULE_0__.ProductsStore.getAll();\n    const hasSampleProducts = existingProducts.some((p)=>p.sku === \"DCL-001\" || p.sku === \"CMG-001\" || p.sku === \"MCL-001\");\n    if (hasSampleProducts) {\n        return; // لا نضيف منتجات إذا كانت المنتجات التجريبية موجودة بالفعل\n    }\n    // إضافة منتجات تجريبية\n    const sampleProducts = [\n        {\n            name: \"عدسات لاصقة يومية\",\n            nameEn: \"Daily Contact Lenses\",\n            sku: \"DCL-001\",\n            category: \"العدسات اليومية\",\n            brand: \"Johnson & Johnson\",\n            price: 25000,\n            priceCurrency: \"IQD\",\n            stock: 50,\n            status: \"active\",\n            image: \"https://picsum.photos/400/400?random=1\",\n            images: [\n                \"https://picsum.photos/400/400?random=1\"\n            ],\n            description: \"عدسات لاصقة يومية عالية الجودة توفر راحة طوال اليوم\",\n            shortDescription: \"عدسات يومية مريحة\",\n            tags: [\n                \"عدسات\",\n                \"يومية\",\n                \"راحة\"\n            ],\n            specifications: [\n                {\n                    key: \"النوع\",\n                    value: \"يومية\"\n                },\n                {\n                    key: \"المادة\",\n                    value: \"هيدروجيل\"\n                },\n                {\n                    key: \"محتوى الماء\",\n                    value: \"58%\"\n                }\n            ],\n            slug: \"daily-contact-lenses-dcl-001\",\n            featured: true,\n            allowBackorder: false,\n            trackQuantity: true,\n            location: \"المخزن الرئيسي\"\n        },\n        {\n            name: \"نظارات طبية كلاسيكية\",\n            nameEn: \"Classic Medical Glasses\",\n            sku: \"CMG-001\",\n            category: \"النظارات الطبية\",\n            brand: \"Ray-Ban\",\n            price: 150000,\n            priceCurrency: \"IQD\",\n            stock: 25,\n            status: \"active\",\n            image: \"https://picsum.photos/400/400?random=2\",\n            images: [\n                \"https://picsum.photos/400/400?random=2\"\n            ],\n            description: \"نظارات طبية أنيقة بتصميم كلاسيكي مناسب لجميع الأعمار\",\n            shortDescription: \"نظارات طبية كلاسيكية\",\n            tags: [\n                \"نظارات\",\n                \"طبية\",\n                \"كلاسيكية\"\n            ],\n            specifications: [\n                {\n                    key: \"المادة\",\n                    value: \"معدن\"\n                },\n                {\n                    key: \"اللون\",\n                    value: \"أسود\"\n                },\n                {\n                    key: \"الحجم\",\n                    value: \"متوسط\"\n                }\n            ],\n            slug: \"classic-medical-glasses-cmg-001\",\n            featured: false,\n            allowBackorder: true,\n            trackQuantity: true,\n            location: \"المخزن الرئيسي\"\n        },\n        {\n            name: \"عدسات ملونة شهرية\",\n            nameEn: \"Monthly Colored Lenses\",\n            sku: \"MCL-001\",\n            category: \"العدسات الملونة\",\n            brand: \"Alcon\",\n            price: 45000,\n            priceCurrency: \"IQD\",\n            stock: 30,\n            status: \"active\",\n            image: \"https://picsum.photos/400/400?random=3\",\n            images: [\n                \"https://picsum.photos/400/400?random=3\"\n            ],\n            description: \"عدسات ملونة شهرية تمنحك إطلالة جذابة ومميزة\",\n            shortDescription: \"عدسات ملونة شهرية\",\n            tags: [\n                \"عدسات\",\n                \"ملونة\",\n                \"شهرية\"\n            ],\n            specifications: [\n                {\n                    key: \"النوع\",\n                    value: \"شهرية\"\n                },\n                {\n                    key: \"اللون\",\n                    value: \"أزرق\"\n                },\n                {\n                    key: \"القطر\",\n                    value: \"14.2 مم\"\n                }\n            ],\n            slug: \"monthly-colored-lenses-mcl-001\",\n            featured: true,\n            allowBackorder: false,\n            trackQuantity: true,\n            location: \"المخزن الرئيسي\"\n        }\n    ];\n    // إضافة المنتجات\n    sampleProducts.forEach((product)=>{\n        _products_store__WEBPACK_IMPORTED_MODULE_0__.ProductsStore.add(product);\n    });\n    console.log(\"تم إضافة المنتجات التجريبية بنجاح\");\n};\n// دالة لمسح المنتجات المكررة\nconst cleanDuplicateProducts = ()=>{\n    const allProducts = _products_store__WEBPACK_IMPORTED_MODULE_0__.ProductsStore.getAll();\n    const uniqueProducts = [];\n    const seenIds = new Set();\n    const seenSkus = new Set();\n    for (const product of allProducts){\n        // التحقق من المعرف و SKU لتجنب التكرار\n        if (!seenIds.has(product.id) && !seenSkus.has(product.sku)) {\n            seenIds.add(product.id);\n            seenSkus.add(product.sku);\n            uniqueProducts.push(product);\n        }\n    }\n    if (uniqueProducts.length !== allProducts.length) {\n        _products_store__WEBPACK_IMPORTED_MODULE_0__.ProductsStore.clear();\n        _products_store__WEBPACK_IMPORTED_MODULE_0__.ProductsStore.import(uniqueProducts);\n        console.log(\"تم مسح \".concat(allProducts.length - uniqueProducts.length, \" منتج مكرر\"));\n        return true;\n    }\n    return false;\n};\n// دالة لإعادة تعيين المنتجات التجريبية بالكامل\nconst resetSampleProducts = ()=>{\n    _products_store__WEBPACK_IMPORTED_MODULE_0__.ProductsStore.clear();\n    initSampleProducts();\n    console.log(\"تم إعادة تعيين المنتجات التجريبية\");\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9saWIvaW5pdC1zYW1wbGUtcHJvZHVjdHMudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUFBLGdDQUFnQztBQUNpQjtBQUUxQyxNQUFNQyxxQkFBcUI7SUFDaEMsc0NBQXNDO0lBQ3RDLE1BQU1DLG1CQUFtQkYsMERBQWFBLENBQUNHLE1BQU07SUFDN0MsTUFBTUMsb0JBQW9CRixpQkFBaUJHLElBQUksQ0FBQ0MsQ0FBQUEsSUFDOUNBLEVBQUVDLEdBQUcsS0FBSyxhQUFhRCxFQUFFQyxHQUFHLEtBQUssYUFBYUQsRUFBRUMsR0FBRyxLQUFLO0lBRzFELElBQUlILG1CQUFtQjtRQUNyQixRQUFRLDJEQUEyRDtJQUNyRTtJQUVBLHVCQUF1QjtJQUN2QixNQUFNSSxpQkFBaUI7UUFDckI7WUFDRUMsTUFBTTtZQUNOQyxRQUFRO1lBQ1JILEtBQUs7WUFDTEksVUFBVTtZQUNWQyxPQUFPO1lBQ1BDLE9BQU87WUFDUEMsZUFBZTtZQUNmQyxPQUFPO1lBQ1BDLFFBQVE7WUFDUkMsT0FBTztZQUNQQyxRQUFRO2dCQUFDO2FBQXlDO1lBQ2xEQyxhQUFhO1lBQ2JDLGtCQUFrQjtZQUNsQkMsTUFBTTtnQkFBQztnQkFBUztnQkFBUzthQUFPO1lBQ2hDQyxnQkFBZ0I7Z0JBQ2Q7b0JBQUVDLEtBQUs7b0JBQVNDLE9BQU87Z0JBQVE7Z0JBQy9CO29CQUFFRCxLQUFLO29CQUFVQyxPQUFPO2dCQUFXO2dCQUNuQztvQkFBRUQsS0FBSztvQkFBZUMsT0FBTztnQkFBTTthQUNwQztZQUNEQyxNQUFNO1lBQ05DLFVBQVU7WUFDVkMsZ0JBQWdCO1lBQ2hCQyxlQUFlO1lBQ2ZDLFVBQVU7UUFDWjtRQUNBO1lBQ0VwQixNQUFNO1lBQ05DLFFBQVE7WUFDUkgsS0FBSztZQUNMSSxVQUFVO1lBQ1ZDLE9BQU87WUFDUEMsT0FBTztZQUNQQyxlQUFlO1lBQ2ZDLE9BQU87WUFDUEMsUUFBUTtZQUNSQyxPQUFPO1lBQ1BDLFFBQVE7Z0JBQUM7YUFBeUM7WUFDbERDLGFBQWE7WUFDYkMsa0JBQWtCO1lBQ2xCQyxNQUFNO2dCQUFDO2dCQUFVO2dCQUFRO2FBQVc7WUFDcENDLGdCQUFnQjtnQkFDZDtvQkFBRUMsS0FBSztvQkFBVUMsT0FBTztnQkFBTztnQkFDL0I7b0JBQUVELEtBQUs7b0JBQVNDLE9BQU87Z0JBQU87Z0JBQzlCO29CQUFFRCxLQUFLO29CQUFTQyxPQUFPO2dCQUFRO2FBQ2hDO1lBQ0RDLE1BQU07WUFDTkMsVUFBVTtZQUNWQyxnQkFBZ0I7WUFDaEJDLGVBQWU7WUFDZkMsVUFBVTtRQUNaO1FBQ0E7WUFDRXBCLE1BQU07WUFDTkMsUUFBUTtZQUNSSCxLQUFLO1lBQ0xJLFVBQVU7WUFDVkMsT0FBTztZQUNQQyxPQUFPO1lBQ1BDLGVBQWU7WUFDZkMsT0FBTztZQUNQQyxRQUFRO1lBQ1JDLE9BQU87WUFDUEMsUUFBUTtnQkFBQzthQUF5QztZQUNsREMsYUFBYTtZQUNiQyxrQkFBa0I7WUFDbEJDLE1BQU07Z0JBQUM7Z0JBQVM7Z0JBQVM7YUFBUTtZQUNqQ0MsZ0JBQWdCO2dCQUNkO29CQUFFQyxLQUFLO29CQUFTQyxPQUFPO2dCQUFRO2dCQUMvQjtvQkFBRUQsS0FBSztvQkFBU0MsT0FBTztnQkFBTztnQkFDOUI7b0JBQUVELEtBQUs7b0JBQVNDLE9BQU87Z0JBQVU7YUFDbEM7WUFDREMsTUFBTTtZQUNOQyxVQUFVO1lBQ1ZDLGdCQUFnQjtZQUNoQkMsZUFBZTtZQUNmQyxVQUFVO1FBQ1o7S0FDRDtJQUVELGlCQUFpQjtJQUNqQnJCLGVBQWVzQixPQUFPLENBQUNDLENBQUFBO1FBQ3JCL0IsMERBQWFBLENBQUNnQyxHQUFHLENBQUNEO0lBQ3BCO0lBRUFFLFFBQVFDLEdBQUcsQ0FBQztBQUNkLEVBQUU7QUFFRiw2QkFBNkI7QUFDdEIsTUFBTUMseUJBQXlCO0lBQ3BDLE1BQU1DLGNBQWNwQywwREFBYUEsQ0FBQ0csTUFBTTtJQUN4QyxNQUFNa0MsaUJBQWlCLEVBQUU7SUFDekIsTUFBTUMsVUFBVSxJQUFJQztJQUNwQixNQUFNQyxXQUFXLElBQUlEO0lBRXJCLEtBQUssTUFBTVIsV0FBV0ssWUFBYTtRQUNqQyx1Q0FBdUM7UUFDdkMsSUFBSSxDQUFDRSxRQUFRRyxHQUFHLENBQUNWLFFBQVFXLEVBQUUsS0FBSyxDQUFDRixTQUFTQyxHQUFHLENBQUNWLFFBQVF4QixHQUFHLEdBQUc7WUFDMUQrQixRQUFRTixHQUFHLENBQUNELFFBQVFXLEVBQUU7WUFDdEJGLFNBQVNSLEdBQUcsQ0FBQ0QsUUFBUXhCLEdBQUc7WUFDeEI4QixlQUFlTSxJQUFJLENBQUNaO1FBQ3RCO0lBQ0Y7SUFFQSxJQUFJTSxlQUFlTyxNQUFNLEtBQUtSLFlBQVlRLE1BQU0sRUFBRTtRQUNoRDVDLDBEQUFhQSxDQUFDNkMsS0FBSztRQUNuQjdDLDBEQUFhQSxDQUFDOEMsTUFBTSxDQUFDVDtRQUNyQkosUUFBUUMsR0FBRyxDQUFDLFVBQXFELE9BQTNDRSxZQUFZUSxNQUFNLEdBQUdQLGVBQWVPLE1BQU0sRUFBQztRQUNqRSxPQUFPO0lBQ1Q7SUFDQSxPQUFPO0FBQ1QsRUFBRTtBQUVGLCtDQUErQztBQUN4QyxNQUFNRyxzQkFBc0I7SUFDakMvQywwREFBYUEsQ0FBQzZDLEtBQUs7SUFDbkI1QztJQUNBZ0MsUUFBUUMsR0FBRyxDQUFDO0FBQ2QsRUFBRSIsInNvdXJjZXMiOlsiRzpcXHZpc2lvbmxlbnNcXHNyY1xcbGliXFxpbml0LXNhbXBsZS1wcm9kdWN0cy50cyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyDYpdi22KfZgdipINmF2YbYqtis2KfYqiDYqtis2LHZitio2YrYqSDZhNmE2KfYrtiq2KjYp9ixXG5pbXBvcnQgeyBQcm9kdWN0c1N0b3JlIH0gZnJvbSAnLi9wcm9kdWN0cy1zdG9yZSc7XG5cbmV4cG9ydCBjb25zdCBpbml0U2FtcGxlUHJvZHVjdHMgPSAoKSA9PiB7XG4gIC8vINin2YTYqtit2YLZgiDZhdmGINmI2KzZiNivINmF2YbYqtis2KfYqiDYqtis2LHZitio2YrYqSDZhdit2K/Yr9ipXG4gIGNvbnN0IGV4aXN0aW5nUHJvZHVjdHMgPSBQcm9kdWN0c1N0b3JlLmdldEFsbCgpO1xuICBjb25zdCBoYXNTYW1wbGVQcm9kdWN0cyA9IGV4aXN0aW5nUHJvZHVjdHMuc29tZShwID0+XG4gICAgcC5za3UgPT09IFwiRENMLTAwMVwiIHx8IHAuc2t1ID09PSBcIkNNRy0wMDFcIiB8fCBwLnNrdSA9PT0gXCJNQ0wtMDAxXCJcbiAgKTtcblxuICBpZiAoaGFzU2FtcGxlUHJvZHVjdHMpIHtcbiAgICByZXR1cm47IC8vINmE2Kcg2YbYttmK2YEg2YXZhtiq2KzYp9iqINil2LDYpyDZg9in2YbYqiDYp9mE2YXZhtiq2KzYp9iqINin2YTYqtis2LHZitio2YrYqSDZhdmI2KzZiNiv2Kkg2KjYp9mE2YHYudmEXG4gIH1cblxuICAvLyDYpdi22KfZgdipINmF2YbYqtis2KfYqiDYqtis2LHZitio2YrYqVxuICBjb25zdCBzYW1wbGVQcm9kdWN0cyA9IFtcbiAgICB7XG4gICAgICBuYW1lOiBcIti52K/Ys9in2Kog2YTYp9i12YLYqSDZitmI2YXZitipXCIsXG4gICAgICBuYW1lRW46IFwiRGFpbHkgQ29udGFjdCBMZW5zZXNcIixcbiAgICAgIHNrdTogXCJEQ0wtMDAxXCIsXG4gICAgICBjYXRlZ29yeTogXCLYp9mE2LnYr9iz2KfYqiDYp9mE2YrZiNmF2YrYqVwiLFxuICAgICAgYnJhbmQ6IFwiSm9obnNvbiAmIEpvaG5zb25cIixcbiAgICAgIHByaWNlOiAyNTAwMCxcbiAgICAgIHByaWNlQ3VycmVuY3k6IFwiSVFEXCIsXG4gICAgICBzdG9jazogNTAsXG4gICAgICBzdGF0dXM6IFwiYWN0aXZlXCIgYXMgY29uc3QsXG4gICAgICBpbWFnZTogXCJodHRwczovL3BpY3N1bS5waG90b3MvNDAwLzQwMD9yYW5kb209MVwiLFxuICAgICAgaW1hZ2VzOiBbXCJodHRwczovL3BpY3N1bS5waG90b3MvNDAwLzQwMD9yYW5kb209MVwiXSxcbiAgICAgIGRlc2NyaXB0aW9uOiBcIti52K/Ys9in2Kog2YTYp9i12YLYqSDZitmI2YXZitipINi52KfZhNmK2Kkg2KfZhNis2YjYr9ipINiq2YjZgdixINix2KfYrdipINi32YjYp9mEINin2YTZitmI2YVcIixcbiAgICAgIHNob3J0RGVzY3JpcHRpb246IFwi2LnYr9iz2KfYqiDZitmI2YXZitipINmF2LHZitit2KlcIixcbiAgICAgIHRhZ3M6IFtcIti52K/Ys9in2KpcIiwgXCLZitmI2YXZitipXCIsIFwi2LHYp9it2KlcIl0sXG4gICAgICBzcGVjaWZpY2F0aW9uczogW1xuICAgICAgICB7IGtleTogXCLYp9mE2YbZiNi5XCIsIHZhbHVlOiBcItmK2YjZhdmK2KlcIiB9LFxuICAgICAgICB7IGtleTogXCLYp9mE2YXYp9iv2KlcIiwgdmFsdWU6IFwi2YfZitiv2LHZiNis2YrZhFwiIH0sXG4gICAgICAgIHsga2V5OiBcItmF2K3YqtmI2Ykg2KfZhNmF2KfYoVwiLCB2YWx1ZTogXCI1OCVcIiB9XG4gICAgICBdLFxuICAgICAgc2x1ZzogXCJkYWlseS1jb250YWN0LWxlbnNlcy1kY2wtMDAxXCIsXG4gICAgICBmZWF0dXJlZDogdHJ1ZSxcbiAgICAgIGFsbG93QmFja29yZGVyOiBmYWxzZSxcbiAgICAgIHRyYWNrUXVhbnRpdHk6IHRydWUsXG4gICAgICBsb2NhdGlvbjogXCLYp9mE2YXYrtiy2YYg2KfZhNix2KbZitiz2YpcIlxuICAgIH0sXG4gICAge1xuICAgICAgbmFtZTogXCLZhti42KfYsdin2Kog2LfYqNmK2Kkg2YPZhNin2LPZitmD2YrYqVwiLFxuICAgICAgbmFtZUVuOiBcIkNsYXNzaWMgTWVkaWNhbCBHbGFzc2VzXCIsXG4gICAgICBza3U6IFwiQ01HLTAwMVwiLFxuICAgICAgY2F0ZWdvcnk6IFwi2KfZhNmG2LjYp9ix2KfYqiDYp9mE2LfYqNmK2KlcIixcbiAgICAgIGJyYW5kOiBcIlJheS1CYW5cIixcbiAgICAgIHByaWNlOiAxNTAwMDAsXG4gICAgICBwcmljZUN1cnJlbmN5OiBcIklRRFwiLFxuICAgICAgc3RvY2s6IDI1LFxuICAgICAgc3RhdHVzOiBcImFjdGl2ZVwiIGFzIGNvbnN0LFxuICAgICAgaW1hZ2U6IFwiaHR0cHM6Ly9waWNzdW0ucGhvdG9zLzQwMC80MDA/cmFuZG9tPTJcIixcbiAgICAgIGltYWdlczogW1wiaHR0cHM6Ly9waWNzdW0ucGhvdG9zLzQwMC80MDA/cmFuZG9tPTJcIl0sXG4gICAgICBkZXNjcmlwdGlvbjogXCLZhti42KfYsdin2Kog2LfYqNmK2Kkg2KPZhtmK2YLYqSDYqNiq2LXZhdmK2YUg2YPZhNin2LPZitmD2Yog2YXZhtin2LPYqCDZhNis2YXZiti5INin2YTYo9i52YXYp9ixXCIsXG4gICAgICBzaG9ydERlc2NyaXB0aW9uOiBcItmG2LjYp9ix2KfYqiDYt9io2YrYqSDZg9mE2KfYs9mK2YPZitipXCIsXG4gICAgICB0YWdzOiBbXCLZhti42KfYsdin2KpcIiwgXCLYt9io2YrYqVwiLCBcItmD2YTYp9iz2YrZg9mK2KlcIl0sXG4gICAgICBzcGVjaWZpY2F0aW9uczogW1xuICAgICAgICB7IGtleTogXCLYp9mE2YXYp9iv2KlcIiwgdmFsdWU6IFwi2YXYudiv2YZcIiB9LFxuICAgICAgICB7IGtleTogXCLYp9mE2YTZiNmGXCIsIHZhbHVlOiBcItij2LPZiNivXCIgfSxcbiAgICAgICAgeyBrZXk6IFwi2KfZhNit2KzZhVwiLCB2YWx1ZTogXCLZhdiq2YjYs9i3XCIgfVxuICAgICAgXSxcbiAgICAgIHNsdWc6IFwiY2xhc3NpYy1tZWRpY2FsLWdsYXNzZXMtY21nLTAwMVwiLFxuICAgICAgZmVhdHVyZWQ6IGZhbHNlLFxuICAgICAgYWxsb3dCYWNrb3JkZXI6IHRydWUsXG4gICAgICB0cmFja1F1YW50aXR5OiB0cnVlLFxuICAgICAgbG9jYXRpb246IFwi2KfZhNmF2K7YstmGINin2YTYsdim2YrYs9mKXCJcbiAgICB9LFxuICAgIHtcbiAgICAgIG5hbWU6IFwi2LnYr9iz2KfYqiDZhdmE2YjZhtipINi02YfYsdmK2KlcIixcbiAgICAgIG5hbWVFbjogXCJNb250aGx5IENvbG9yZWQgTGVuc2VzXCIsXG4gICAgICBza3U6IFwiTUNMLTAwMVwiLFxuICAgICAgY2F0ZWdvcnk6IFwi2KfZhNi52K/Ys9in2Kog2KfZhNmF2YTZiNmG2KlcIixcbiAgICAgIGJyYW5kOiBcIkFsY29uXCIsXG4gICAgICBwcmljZTogNDUwMDAsXG4gICAgICBwcmljZUN1cnJlbmN5OiBcIklRRFwiLFxuICAgICAgc3RvY2s6IDMwLFxuICAgICAgc3RhdHVzOiBcImFjdGl2ZVwiIGFzIGNvbnN0LFxuICAgICAgaW1hZ2U6IFwiaHR0cHM6Ly9waWNzdW0ucGhvdG9zLzQwMC80MDA/cmFuZG9tPTNcIixcbiAgICAgIGltYWdlczogW1wiaHR0cHM6Ly9waWNzdW0ucGhvdG9zLzQwMC80MDA/cmFuZG9tPTNcIl0sXG4gICAgICBkZXNjcmlwdGlvbjogXCLYudiv2LPYp9iqINmF2YTZiNmG2Kkg2LTZh9ix2YrYqSDYqtmF2YbYrdmDINil2LfZhNin2YTYqSDYrNiw2KfYqNipINmI2YXZhdmK2LLYqVwiLFxuICAgICAgc2hvcnREZXNjcmlwdGlvbjogXCLYudiv2LPYp9iqINmF2YTZiNmG2Kkg2LTZh9ix2YrYqVwiLFxuICAgICAgdGFnczogW1wi2LnYr9iz2KfYqlwiLCBcItmF2YTZiNmG2KlcIiwgXCLYtNmH2LHZitipXCJdLFxuICAgICAgc3BlY2lmaWNhdGlvbnM6IFtcbiAgICAgICAgeyBrZXk6IFwi2KfZhNmG2YjYuVwiLCB2YWx1ZTogXCLYtNmH2LHZitipXCIgfSxcbiAgICAgICAgeyBrZXk6IFwi2KfZhNmE2YjZhlwiLCB2YWx1ZTogXCLYo9iy2LHZglwiIH0sXG4gICAgICAgIHsga2V5OiBcItin2YTZgti32LFcIiwgdmFsdWU6IFwiMTQuMiDZhdmFXCIgfVxuICAgICAgXSxcbiAgICAgIHNsdWc6IFwibW9udGhseS1jb2xvcmVkLWxlbnNlcy1tY2wtMDAxXCIsXG4gICAgICBmZWF0dXJlZDogdHJ1ZSxcbiAgICAgIGFsbG93QmFja29yZGVyOiBmYWxzZSxcbiAgICAgIHRyYWNrUXVhbnRpdHk6IHRydWUsXG4gICAgICBsb2NhdGlvbjogXCLYp9mE2YXYrtiy2YYg2KfZhNix2KbZitiz2YpcIlxuICAgIH1cbiAgXTtcblxuICAvLyDYpdi22KfZgdipINin2YTZhdmG2KrYrNin2KpcbiAgc2FtcGxlUHJvZHVjdHMuZm9yRWFjaChwcm9kdWN0ID0+IHtcbiAgICBQcm9kdWN0c1N0b3JlLmFkZChwcm9kdWN0KTtcbiAgfSk7XG5cbiAgY29uc29sZS5sb2coXCLYqtmFINil2LbYp9mB2Kkg2KfZhNmF2YbYqtis2KfYqiDYp9mE2KrYrNix2YrYqNmK2Kkg2KjZhtis2KfYrVwiKTtcbn07XG5cbi8vINiv2KfZhNipINmE2YXYs9itINin2YTZhdmG2KrYrNin2Kog2KfZhNmF2YPYsdix2KlcbmV4cG9ydCBjb25zdCBjbGVhbkR1cGxpY2F0ZVByb2R1Y3RzID0gKCkgPT4ge1xuICBjb25zdCBhbGxQcm9kdWN0cyA9IFByb2R1Y3RzU3RvcmUuZ2V0QWxsKCk7XG4gIGNvbnN0IHVuaXF1ZVByb2R1Y3RzID0gW107XG4gIGNvbnN0IHNlZW5JZHMgPSBuZXcgU2V0KCk7XG4gIGNvbnN0IHNlZW5Ta3VzID0gbmV3IFNldCgpO1xuXG4gIGZvciAoY29uc3QgcHJvZHVjdCBvZiBhbGxQcm9kdWN0cykge1xuICAgIC8vINin2YTYqtit2YLZgiDZhdmGINin2YTZhdi52LHZgSDZiCBTS1Ug2YTYqtis2YbYqCDYp9mE2KrZg9ix2KfYsVxuICAgIGlmICghc2Vlbklkcy5oYXMocHJvZHVjdC5pZCkgJiYgIXNlZW5Ta3VzLmhhcyhwcm9kdWN0LnNrdSkpIHtcbiAgICAgIHNlZW5JZHMuYWRkKHByb2R1Y3QuaWQpO1xuICAgICAgc2VlblNrdXMuYWRkKHByb2R1Y3Quc2t1KTtcbiAgICAgIHVuaXF1ZVByb2R1Y3RzLnB1c2gocHJvZHVjdCk7XG4gICAgfVxuICB9XG5cbiAgaWYgKHVuaXF1ZVByb2R1Y3RzLmxlbmd0aCAhPT0gYWxsUHJvZHVjdHMubGVuZ3RoKSB7XG4gICAgUHJvZHVjdHNTdG9yZS5jbGVhcigpO1xuICAgIFByb2R1Y3RzU3RvcmUuaW1wb3J0KHVuaXF1ZVByb2R1Y3RzKTtcbiAgICBjb25zb2xlLmxvZyhg2KrZhSDZhdiz2K0gJHthbGxQcm9kdWN0cy5sZW5ndGggLSB1bmlxdWVQcm9kdWN0cy5sZW5ndGh9INmF2YbYqtisINmF2YPYsdixYCk7XG4gICAgcmV0dXJuIHRydWU7XG4gIH1cbiAgcmV0dXJuIGZhbHNlO1xufTtcblxuLy8g2K/Yp9mE2Kkg2YTYpdi52KfYr9ipINiq2LnZitmK2YYg2KfZhNmF2YbYqtis2KfYqiDYp9mE2KrYrNix2YrYqNmK2Kkg2KjYp9mE2YPYp9mF2YRcbmV4cG9ydCBjb25zdCByZXNldFNhbXBsZVByb2R1Y3RzID0gKCkgPT4ge1xuICBQcm9kdWN0c1N0b3JlLmNsZWFyKCk7XG4gIGluaXRTYW1wbGVQcm9kdWN0cygpO1xuICBjb25zb2xlLmxvZyhcItiq2YUg2KXYudin2K/YqSDYqti52YrZitmGINin2YTZhdmG2KrYrNin2Kog2KfZhNiq2KzYsdmK2KjZitipXCIpO1xufTtcbiJdLCJuYW1lcyI6WyJQcm9kdWN0c1N0b3JlIiwiaW5pdFNhbXBsZVByb2R1Y3RzIiwiZXhpc3RpbmdQcm9kdWN0cyIsImdldEFsbCIsImhhc1NhbXBsZVByb2R1Y3RzIiwic29tZSIsInAiLCJza3UiLCJzYW1wbGVQcm9kdWN0cyIsIm5hbWUiLCJuYW1lRW4iLCJjYXRlZ29yeSIsImJyYW5kIiwicHJpY2UiLCJwcmljZUN1cnJlbmN5Iiwic3RvY2siLCJzdGF0dXMiLCJpbWFnZSIsImltYWdlcyIsImRlc2NyaXB0aW9uIiwic2hvcnREZXNjcmlwdGlvbiIsInRhZ3MiLCJzcGVjaWZpY2F0aW9ucyIsImtleSIsInZhbHVlIiwic2x1ZyIsImZlYXR1cmVkIiwiYWxsb3dCYWNrb3JkZXIiLCJ0cmFja1F1YW50aXR5IiwibG9jYXRpb24iLCJmb3JFYWNoIiwicHJvZHVjdCIsImFkZCIsImNvbnNvbGUiLCJsb2ciLCJjbGVhbkR1cGxpY2F0ZVByb2R1Y3RzIiwiYWxsUHJvZHVjdHMiLCJ1bmlxdWVQcm9kdWN0cyIsInNlZW5JZHMiLCJTZXQiLCJzZWVuU2t1cyIsImhhcyIsImlkIiwicHVzaCIsImxlbmd0aCIsImNsZWFyIiwiaW1wb3J0IiwicmVzZXRTYW1wbGVQcm9kdWN0cyJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/init-sample-products.ts\n"));

/***/ })

});