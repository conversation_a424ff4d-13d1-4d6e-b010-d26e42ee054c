"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/debug-products/page",{

/***/ "(app-pages-browser)/./src/app/admin/debug-products/page.tsx":
/*!***********************************************!*\
  !*** ./src/app/admin/debug-products/page.tsx ***!
  \***********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DebugProductsPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_products_store__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/products-store */ \"(app-pages-browser)/./src/lib/products-store.ts\");\n/* harmony import */ var _lib_init_sample_products__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/init-sample-products */ \"(app-pages-browser)/./src/lib/init-sample-products.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction DebugProductsPage() {\n    _s();\n    const [products, setProducts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"DebugProductsPage.useEffect\": ()=>{\n            // تنظيف المنتجات المكررة أولاً\n            (0,_lib_init_sample_products__WEBPACK_IMPORTED_MODULE_3__.cleanDuplicateProducts)();\n            // إضافة منتجات تجريبية\n            (0,_lib_init_sample_products__WEBPACK_IMPORTED_MODULE_3__.initSampleProducts)();\n            // تحميل المنتجات\n            const allProducts = _lib_products_store__WEBPACK_IMPORTED_MODULE_2__.ProductsStore.getAll();\n            setProducts(allProducts);\n        }\n    }[\"DebugProductsPage.useEffect\"], []);\n    const clearProducts = ()=>{\n        _lib_products_store__WEBPACK_IMPORTED_MODULE_2__.ProductsStore.clear();\n        setProducts([]);\n    };\n    const addSampleProducts = ()=>{\n        (0,_lib_init_sample_products__WEBPACK_IMPORTED_MODULE_3__.resetSampleProducts)(); // إعادة تعيين كاملة\n        const allProducts = _lib_products_store__WEBPACK_IMPORTED_MODULE_2__.ProductsStore.getAll();\n        setProducts(allProducts);\n    };\n    const cleanDuplicates = ()=>{\n        const cleaned = (0,_lib_init_sample_products__WEBPACK_IMPORTED_MODULE_3__.cleanDuplicateProducts)();\n        const allProducts = _lib_products_store__WEBPACK_IMPORTED_MODULE_2__.ProductsStore.getAll();\n        setProducts(allProducts);\n        if (cleaned) {\n            alert(\"تم تنظيف المنتجات المكررة!\");\n        } else {\n            alert(\"لا توجد منتجات مكررة\");\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"p-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                className: \"text-2xl font-bold mb-4\",\n                children: \"تصحيح أخطاء المنتجات\"\n            }, void 0, false, {\n                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\debug-products\\\\page.tsx\",\n                lineNumber: 46,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-4 flex flex-wrap gap-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: addSampleProducts,\n                        className: \"bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600\",\n                        children: \"إعادة تعيين المنتجات التجريبية\"\n                    }, void 0, false, {\n                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\debug-products\\\\page.tsx\",\n                        lineNumber: 49,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: cleanDuplicates,\n                        className: \"bg-yellow-500 text-white px-4 py-2 rounded hover:bg-yellow-600\",\n                        children: \"تنظيف المنتجات المكررة\"\n                    }, void 0, false, {\n                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\debug-products\\\\page.tsx\",\n                        lineNumber: 55,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: clearProducts,\n                        className: \"bg-red-500 text-white px-4 py-2 rounded hover:bg-red-600\",\n                        children: \"مسح جميع المنتجات\"\n                    }, void 0, false, {\n                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\debug-products\\\\page.tsx\",\n                        lineNumber: 61,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>window.location.reload(),\n                        className: \"bg-gray-500 text-white px-4 py-2 rounded hover:bg-gray-600\",\n                        children: \"تحديث الصفحة\"\n                    }, void 0, false, {\n                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\debug-products\\\\page.tsx\",\n                        lineNumber: 67,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\debug-products\\\\page.tsx\",\n                lineNumber: 48,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gray-100 p-4 rounded\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-lg font-semibold mb-2\",\n                        children: [\n                            \"المنتجات الحالية (\",\n                            products.length,\n                            \"):\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\debug-products\\\\page.tsx\",\n                        lineNumber: 76,\n                        columnNumber: 9\n                    }, this),\n                    products.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        children: \"لا توجد منتجات\"\n                    }, void 0, false, {\n                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\debug-products\\\\page.tsx\",\n                        lineNumber: 78,\n                        columnNumber: 11\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-2\",\n                        children: products.map((product, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white p-3 rounded border\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                            children: [\n                                                \"#\",\n                                                index + 1\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\debug-products\\\\page.tsx\",\n                                            lineNumber: 83,\n                                            columnNumber: 20\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\debug-products\\\\page.tsx\",\n                                        lineNumber: 83,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"المعرف:\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\debug-products\\\\page.tsx\",\n                                                lineNumber: 84,\n                                                columnNumber: 20\n                                            }, this),\n                                            \" \",\n                                            product.id\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\debug-products\\\\page.tsx\",\n                                        lineNumber: 84,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"الاسم:\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\debug-products\\\\page.tsx\",\n                                                lineNumber: 85,\n                                                columnNumber: 20\n                                            }, this),\n                                            \" \",\n                                            product.name\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\debug-products\\\\page.tsx\",\n                                        lineNumber: 85,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"SKU:\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\debug-products\\\\page.tsx\",\n                                                lineNumber: 86,\n                                                columnNumber: 20\n                                            }, this),\n                                            \" \",\n                                            product.sku\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\debug-products\\\\page.tsx\",\n                                        lineNumber: 86,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"رابط التعديل:\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\debug-products\\\\page.tsx\",\n                                                lineNumber: 87,\n                                                columnNumber: 20\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: \"/admin/products/\".concat(product.id, \"/edit\"),\n                                                className: \"text-blue-500 hover:underline ml-2\",\n                                                target: \"_blank\",\n                                                children: [\n                                                    \"/admin/products/\",\n                                                    product.id,\n                                                    \"/edit\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\debug-products\\\\page.tsx\",\n                                                lineNumber: 88,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\debug-products\\\\page.tsx\",\n                                        lineNumber: 87,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"رابط العرض:\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\debug-products\\\\page.tsx\",\n                                                lineNumber: 96,\n                                                columnNumber: 20\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: \"/admin/products/\".concat(product.id),\n                                                className: \"text-green-500 hover:underline ml-2\",\n                                                target: \"_blank\",\n                                                children: [\n                                                    \"/admin/products/\",\n                                                    product.id\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\debug-products\\\\page.tsx\",\n                                                lineNumber: 97,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\debug-products\\\\page.tsx\",\n                                        lineNumber: 96,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, product.id, true, {\n                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\debug-products\\\\page.tsx\",\n                                lineNumber: 82,\n                                columnNumber: 15\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\debug-products\\\\page.tsx\",\n                        lineNumber: 80,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\debug-products\\\\page.tsx\",\n                lineNumber: 75,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\debug-products\\\\page.tsx\",\n        lineNumber: 45,\n        columnNumber: 5\n    }, this);\n}\n_s(DebugProductsPage, \"f86L6rLANGURv6GE6gupp7+oOp4=\");\n_c = DebugProductsPage;\nvar _c;\n$RefreshReg$(_c, \"DebugProductsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/admin/debug-products/page.tsx\n"));

/***/ })

});