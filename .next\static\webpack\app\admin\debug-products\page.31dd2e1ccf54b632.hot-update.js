"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/debug-products/page",{

/***/ "(app-pages-browser)/./src/app/admin/debug-products/page.tsx":
/*!***********************************************!*\
  !*** ./src/app/admin/debug-products/page.tsx ***!
  \***********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DebugProductsPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_products_store__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/products-store */ \"(app-pages-browser)/./src/lib/products-store.ts\");\n/* harmony import */ var _lib_init_sample_products__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/init-sample-products */ \"(app-pages-browser)/./src/lib/init-sample-products.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction DebugProductsPage() {\n    _s();\n    const [products, setProducts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"DebugProductsPage.useEffect\": ()=>{\n            // تنظيف المنتجات المكررة أولاً\n            (0,_lib_init_sample_products__WEBPACK_IMPORTED_MODULE_3__.cleanDuplicateProducts)();\n            // إضافة منتجات تجريبية\n            (0,_lib_init_sample_products__WEBPACK_IMPORTED_MODULE_3__.initSampleProducts)();\n            // تحميل المنتجات\n            const allProducts = _lib_products_store__WEBPACK_IMPORTED_MODULE_2__.ProductsStore.getAll();\n            setProducts(allProducts);\n        }\n    }[\"DebugProductsPage.useEffect\"], []);\n    const clearProducts = ()=>{\n        _lib_products_store__WEBPACK_IMPORTED_MODULE_2__.ProductsStore.clear();\n        setProducts([]);\n    };\n    const addSampleProducts = ()=>{\n        _lib_products_store__WEBPACK_IMPORTED_MODULE_2__.ProductsStore.clear(); // مسح المنتجات الموجودة\n        (0,_lib_init_sample_products__WEBPACK_IMPORTED_MODULE_3__.initSampleProducts)(); // إضافة منتجات جديدة\n        const allProducts = _lib_products_store__WEBPACK_IMPORTED_MODULE_2__.ProductsStore.getAll();\n        setProducts(allProducts);\n    };\n    const cleanDuplicates = ()=>{\n        (0,_lib_init_sample_products__WEBPACK_IMPORTED_MODULE_3__.cleanDuplicateProducts)();\n        const allProducts = _lib_products_store__WEBPACK_IMPORTED_MODULE_2__.ProductsStore.getAll();\n        setProducts(allProducts);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"p-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                className: \"text-2xl font-bold mb-4\",\n                children: \"تصحيح أخطاء المنتجات\"\n            }, void 0, false, {\n                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\debug-products\\\\page.tsx\",\n                lineNumber: 42,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-4 space-x-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: addSampleProducts,\n                        className: \"bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600\",\n                        children: \"إضافة منتجات تجريبية\"\n                    }, void 0, false, {\n                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\debug-products\\\\page.tsx\",\n                        lineNumber: 45,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: cleanDuplicates,\n                        className: \"bg-yellow-500 text-white px-4 py-2 rounded hover:bg-yellow-600\",\n                        children: \"تنظيف المنتجات المكررة\"\n                    }, void 0, false, {\n                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\debug-products\\\\page.tsx\",\n                        lineNumber: 51,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: clearProducts,\n                        className: \"bg-red-500 text-white px-4 py-2 rounded hover:bg-red-600\",\n                        children: \"مسح جميع المنتجات\"\n                    }, void 0, false, {\n                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\debug-products\\\\page.tsx\",\n                        lineNumber: 57,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\debug-products\\\\page.tsx\",\n                lineNumber: 44,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gray-100 p-4 rounded\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-lg font-semibold mb-2\",\n                        children: [\n                            \"المنتجات الحالية (\",\n                            products.length,\n                            \"):\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\debug-products\\\\page.tsx\",\n                        lineNumber: 66,\n                        columnNumber: 9\n                    }, this),\n                    products.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        children: \"لا توجد منتجات\"\n                    }, void 0, false, {\n                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\debug-products\\\\page.tsx\",\n                        lineNumber: 68,\n                        columnNumber: 11\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-2\",\n                        children: products.map((product, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white p-3 rounded border\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                            children: [\n                                                \"#\",\n                                                index + 1\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\debug-products\\\\page.tsx\",\n                                            lineNumber: 73,\n                                            columnNumber: 20\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\debug-products\\\\page.tsx\",\n                                        lineNumber: 73,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"المعرف:\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\debug-products\\\\page.tsx\",\n                                                lineNumber: 74,\n                                                columnNumber: 20\n                                            }, this),\n                                            \" \",\n                                            product.id\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\debug-products\\\\page.tsx\",\n                                        lineNumber: 74,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"الاسم:\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\debug-products\\\\page.tsx\",\n                                                lineNumber: 75,\n                                                columnNumber: 20\n                                            }, this),\n                                            \" \",\n                                            product.name\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\debug-products\\\\page.tsx\",\n                                        lineNumber: 75,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"SKU:\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\debug-products\\\\page.tsx\",\n                                                lineNumber: 76,\n                                                columnNumber: 20\n                                            }, this),\n                                            \" \",\n                                            product.sku\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\debug-products\\\\page.tsx\",\n                                        lineNumber: 76,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"رابط التعديل:\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\debug-products\\\\page.tsx\",\n                                                lineNumber: 77,\n                                                columnNumber: 20\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: \"/admin/products/\".concat(product.id, \"/edit\"),\n                                                className: \"text-blue-500 hover:underline ml-2\",\n                                                target: \"_blank\",\n                                                children: [\n                                                    \"/admin/products/\",\n                                                    product.id,\n                                                    \"/edit\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\debug-products\\\\page.tsx\",\n                                                lineNumber: 78,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\debug-products\\\\page.tsx\",\n                                        lineNumber: 77,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"رابط العرض:\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\debug-products\\\\page.tsx\",\n                                                lineNumber: 86,\n                                                columnNumber: 20\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: \"/admin/products/\".concat(product.id),\n                                                className: \"text-green-500 hover:underline ml-2\",\n                                                target: \"_blank\",\n                                                children: [\n                                                    \"/admin/products/\",\n                                                    product.id\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\debug-products\\\\page.tsx\",\n                                                lineNumber: 87,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\debug-products\\\\page.tsx\",\n                                        lineNumber: 86,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, product.id, true, {\n                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\debug-products\\\\page.tsx\",\n                                lineNumber: 72,\n                                columnNumber: 15\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\debug-products\\\\page.tsx\",\n                        lineNumber: 70,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\debug-products\\\\page.tsx\",\n                lineNumber: 65,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\debug-products\\\\page.tsx\",\n        lineNumber: 41,\n        columnNumber: 5\n    }, this);\n}\n_s(DebugProductsPage, \"f86L6rLANGURv6GE6gupp7+oOp4=\");\n_c = DebugProductsPage;\nvar _c;\n$RefreshReg$(_c, \"DebugProductsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/admin/debug-products/page.tsx\n"));

/***/ })

});